{"name": "copex/magento2-skeleton", "license": ["OSL-3.0", "AFL-3.0"], "type": "project", "version": "2.4.5", "description": "Magento 2 CE Skeleton", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://copex.io/"}], "repositories": {"amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "swissup": {"type": "composer", "url": "https://docs.swissuplabs.com/packages/"}, "0": {"type": "composer", "url": "https://repo.magento.com/"}, "firegento_magesetup": {"type": "vcs", "url": "**************:firegento/firegento-magesetup2.git"}, "cookie": {"type": "git", "url": "https://github.com/CopeX/cookie-notification-m2.git"}}, "require": {"magento/product-community-edition": "2.4.6-p11", "firegento/magesetup2": "^1.0", "firegento/fastsimpleimport": "^1.3", "splendidinternet/mage2-locale-de-de": "^1.15", "ethanyehuda/magento2-cronjobmanager": "^2", "avstudnitz/scopehint2": "1.3.0", "league/csv": "^8.0", "cweagans/composer-patches": "^1.7", "experius/emailcatcher": "^3.1", "fooman/emailattachments-m2": "^3.3.1", "swissup/module-search-mysql-legacy": "^1.1", "reessolutions/db-override": "^1.0", "yireo/magento2-disable-csp": "^1.0", "amasty/module-custom-forms-lite-subscription-package": "^1.21", "olegkoval/magento2-regenerate-url-rewrites": "~1.7.0"}, "require-dev": {"pdepend/pdepend": "2.*", "lusitanian/oauth": "~0.8.10", "msp/devtools": "^1.2", "mage2tv/magento-cache-clean": "^1.0", "magepal/magento2-preview-checkout-success-page": "^1.1"}, "replace": {"magento/module-authorizenet": "*", "magento/module-braintree": "*", "magento/module-bundle-import-export": "*", "magento/module-dhl": "*", "magento/module-elasticsearch6": "*", "magento/module-elasticsearch7": "*", "magento/module-downloadable-import-export": "*", "magento/module-configurable-import-export": "*", "magento/module-signifyd": "*", "magento/module-fedex": "*", "magento/module-tax-import-export": "*", "magento/module-sampledata": "*", "magento/module-grouped-import-export": "*", "magento/module-marketplace": "*", "magento/module-multishipping": "*", "magento/module-new-relic-reporting": "*", "magento/module-version": "*", "magento/module-ups": "*", "magento/module-usps": "*", "shopialfb/facebook-module": "*", "temando/module-shipping-m2": "*", "braintree/braintree_php": "*", "dotmailer/dotmailer-magento2-extension": "*", "magento/module-analytics": "*", "magento/module-admin-analytics": "*", "magento/module-catalog-analytics": "*", "magento/module-customer-analytics": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-wishlist-analytics": "*", "vertex/product-magento-module": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "vertexinc/product-magento-module": "*", "klarna/module-ordermanagement": "*", "klarna/module-kp": "*", "klarna/module-core": "*", "magento/google-shopping-ads": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-authorizenet-graph-ql": "*", "magento/module-braintree-graph-ql": "*", "magento/module-cardinal-commerce": "*", "magento/module-cybersource": "*", "magento/module-eway": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-persistent": "*", "magento/module-sample-data": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-swatches": "*", "magento/module-swatches-layered-navigation": "*", "magento/module-swatches-graph-ql": "*", "magento/module-worldpay": "*", "magento/module-bundle-graph-ql": "*", "magento/module-catalog-graph-ql": "*", "magento/module-catalog-inventory-graph-ql": "*", "magento/module-catalog-url-rewrite-graph-ql": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-cms-graph-ql": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-catalog-customer-graph-ql": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-configurable-product-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-customer-graph-ql": "*", "magento/module-directory-graph-ql": "*", "magento/module-downloadable-graph-ql": "*", "magento/module-eav-graph-ql": "*", "magento/module-graph-ql": "*", "magento/module-graph-ql-cache": "*", "magento/module-grouped-product-graph-ql": "*", "magento/module-quote-graph-ql": "*", "magento/module-related-product-graph-ql": "*", "magento/module-paypal-graph-ql": "*", "magento/module-sales-graph-ql": "*", "magento/module-store-graph-ql": "*", "magento/module-tax-graph-ql": "*", "magento/module-theme-graph-ql": "*", "magento/module-url-rewrite-graph-ql": "*", "magento/module-vault-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-wishlist-graph-ql": "*", "amzn/amazon-pay-and-login-magento-2-module": "*", "amzn/amazon-pay-and-login-with-amazon-core-module": "*", "amzn/amazon-pay-module": "*", "amzn/amazon-pay-sdk-php": "*", "amzn/login-with-amazon-module": "*", "astock/stock-api-libphp": "*", "braintree/braintree": "*", "dotmailer/dotmailer-magento2-extension-b2b": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-enterprise-package": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-sms": "*", "klarna/m2-payments": "*", "temando/module-shipping": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/module-adobe-ims": "*", "magento/module-admin-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-google-optimizer-staging": "*", "magento/module-inventory": "*", "magento/module-inventory-admin-ui": "*", "magento/module-inventory-advanced-checkout": "*", "magento/module-inventory-api": "*", "magento/module-inventory-bundle-import-export": "*", "magento/module-inventory-bundle-product": "*", "magento/module-inventory-bundle-product-admin-ui": "*", "magento/module-inventory-bundle-product-indexer": "*", "magento/module-inventory-cache": "*", "magento/module-inventory-catalog": "*", "magento/module-inventory-catalog-admin-ui": "*", "magento/module-inventory-catalog-api": "*", "magento/module-inventory-catalog-frontend-ui": "*", "magento/module-inventory-catalog-search": "*", "magento/module-inventory-catalog-search-bundle-product": "*", "magento/module-inventory-catalog-search-configurable-product": "*", "magento/module-inventory-configurable-product": "*", "magento/module-inventory-configurable-product-admin-ui": "*", "magento/module-inventory-configurable-product-frontend-ui": "*", "magento/module-inventory-configurable-product-indexer": "*", "magento/module-inventory-configuration": "*", "magento/module-inventory-configuration-api": "*", "magento/module-inventory-distance-based-source-selection": "*", "magento/module-inventory-distance-based-source-selection-admin-ui": "*", "magento/module-inventory-distance-based-source-selection-api": "*", "magento/module-inventory-elasticsearch": "*", "magento/module-inventory-export-stock": "*", "magento/module-inventory-export-stock-api": "*", "magento/module-inventory-graph-ql": "*", "magento/module-inventory-grouped-product": "*", "magento/module-inventory-grouped-product-admin-ui": "*", "magento/module-inventory-grouped-product-indexer": "*", "magento/module-inventory-import-export": "*", "magento/module-inventory-in-store-pickup": "*", "magento/module-inventory-in-store-pickup-admin-ui": "*", "magento/module-inventory-in-store-pickup-api": "*", "magento/module-inventory-in-store-pickup-frontend": "*", "magento/module-inventory-in-store-pickup-graph-ql": "*", "magento/module-inventory-in-store-pickup-multishipping": "*", "magento/module-inventory-in-store-pickup-quote": "*", "magento/module-inventory-in-store-pickup-quote-graph-ql": "*", "magento/module-inventory-in-store-pickup-sales": "*", "magento/module-inventory-in-store-pickup-sales-admin-ui": "*", "magento/module-inventory-in-store-pickup-sales-api": "*", "magento/module-inventory-in-store-pickup-shipping": "*", "magento/module-inventory-in-store-pickup-shipping-admin-ui": "*", "magento/module-inventory-in-store-pickup-shipping-api": "*", "magento/module-inventory-in-store-pickup-webapi-extension": "*", "magento/module-inventory-indexer": "*", "magento/module-inventory-low-quantity-notification": "*", "magento/module-inventory-low-quantity-notification-admin-ui": "*", "magento/module-inventory-low-quantity-notification-api": "*", "magento/module-inventory-multi-dimensional-indexer-api": "*", "magento/module-inventory-product-alert": "*", "magento/module-inventory-quote-graph-ql": "*", "magento/module-inventory-requisition-list": "*", "magento/module-inventory-reservation-cli": "*", "magento/module-inventory-reservations": "*", "magento/module-inventory-reservations-api": "*", "magento/module-inventory-sales": "*", "magento/module-inventory-sales-admin-ui": "*", "magento/module-inventory-sales-api": "*", "magento/module-inventory-sales-frontend-ui": "*", "magento/module-inventory-setup-fixture-generator": "*", "magento/module-inventory-shipping": "*", "magento/module-inventory-shipping-admin-ui": "*", "magento/module-inventory-source-deduction-api": "*", "magento/module-inventory-source-selection": "*", "magento/module-inventory-source-selection-api": "*", "magento/module-inventory-swatches-frontend-ui": "*", "magento/module-inventory-visual-merchandiser": "*", "magento/module-inventory-wishlist": "*", "paypal/module-braintree": "*", "paypal/module-braintree-core": "*", "paypal/module-braintree-graph-ql": "*", "temando/module-shipping-remover": "*", "vertex/module-address-validation": "*", "vertex/module-tax-staging": "*", "vertexinc/module-tax-staging": "*", "vertexinc/product-magento-module-commerce": "*", "magento/module-catalog-customer-ql": "*", "magento/module-customer-balance-graph-ql": "*", "magento/module-gift-card-account-graph-ql": "*", "magento/module-gift-card-graph-ql": "*", "magento/module-reward-graph-ql": "*", "magento/module-rma-graph-ql": "*", "magento/module-aws-s3-page-builder": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-two-factor-auth": "*", "magento/module-catalog-rule-graph-ql": "*", "magento/module-compare-list-graph-ql": "*", "magento/module-gift-message-graph-ql": "*", "magento/module-login-as-customer-graph-ql": "*", "magento/module-newsletter-graph-ql": "*", "magento/module-newsletter-graph-ql-pwa": "*", "magento/module-re-captcha-graph-ql-pwa": "*", "magento/module-re-captcha-webapi-graph-ql": "*", "magento/module-review-graph-ql": "*", "magento/module-payment-graph-ql": "*"}, "config": {"use-include-path": true, "allow-plugins": {"magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "laminas/laminas-dependency-plugin": true, "cweagans/composer-patches": true, "magento/composer-dependency-version-audit-plugin": true, "dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**", ".giti<PERSON>re"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "scripts": {"post-install-cmd": ["chmod ug+x bin/magento", "if [ ! -e bin/n98-magerun2 ]; then ln -s ../vendor/n98/magerun2/bin/n98-magerun2  bin/n98-magerun2; fi"]}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "patches": {"firegento/magesetup2": {"Firegento": "config/patches/magesetup.patch"}, "magento/framework": {"message-a-tag-fix": "config/patches/framework/message-a-tag-fix.patch", "nosodium": "config/patches/framework/nosodium.patch", "p38214": "config/patches/magento/framework/38214.patch"}, "experius/emailcactcher": {"show_all_in_log": "config/patches/experius/emailcatcher/show_all_in_log.patch"}, "reessolutions/db-override": {"db-version": "config/patches/reessolutions/db-override/db_version.patch"}}, "composer-exit-on-patch-failure": true}}