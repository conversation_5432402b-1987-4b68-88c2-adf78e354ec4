<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Block;

use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\View\Element\Template;
use Magento\Widget\Block\BlockInterface;

class Color extends Template implements BlockInterface
{
    public function prepareElementHtml(AbstractElement $element): AbstractElement
    {
        $defaultColor = '#00CC00';
        $value = $element->getValue() ?: $defaultColor;
        $element->setData('after_element_html', '
                <input type="text"
                    style="height: 50px; width: 70px;"
                    value="' . $value . '"
                    id="' . $element->getHtmlId() . '"
                    name="' . $element->getName() . '"
                >
                <script type="text/javascript">
                require(["jquery", "jquery/colorpicker/js/colorpicker"], function ($) {
                    $currentElement = $("#' . $element->getHtmlId() . '");
                    $currentElement.css("backgroundColor", "'. $value .'");
                    $currentElement.ColorPicker({
                        color: "' . $value . '",
                        onChange: function (hsb, hex, rgb) {
                            $currentElement.css("backgroundColor", "#" + hex).val("#" + hex);
                        }
                    });
                });
                </script><style>.colorpicker {z-index: 10010}</style>');
        $element->setValue(null);

        return $element;
    }
}
