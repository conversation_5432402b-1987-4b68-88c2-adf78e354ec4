<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Block;

use Magento\Backend\Block\Template\Context;
use Magento\Backend\Block\Widget\Form\Element;
use Magento\Cms\Model\Wysiwyg\Config;
use Magento\Framework\Data\Form\Element\AbstractElement;
use Magento\Framework\Data\Form\Element\Factory;

class Wysiwyg extends Element
{
    private Factory $elementFactory;

    private Config $wysiwygConfig;

    public function __construct(
        Context $context,
        Factory $elementFactory,
        Config $config,
        array $data = []
    ) {
        $this->elementFactory = $elementFactory;
        $this->wysiwygConfig = $config;
        parent::__construct($context, $data);
    }

    public function prepareElementHtml(AbstractElement $element): AbstractElement
    {
        $input = $this->elementFactory->create('editor', ['data' => $element->getData()])
            ->setId($element->getId())
            ->setWysiwyg(true)
            ->setConfig($this->wysiwygConfig->getConfig(['add_variables' => false, 'add_widgets' => false]))
            ->setForceLoad(true)
            ->setForm($element->getForm());
        if ($element->getRequired()) {
            $input->addClass('required-entry');
        }
        $element->setData('after_element_html', $this->_getAfterElementHtml() . $input->getElementHtml());

        return $element;
    }

    protected function _getAfterElementHtml()
    {
        return <<<HTML
            <style>
                .admin__field-control.control .control-value {
                    display: none !important;
                }
            </style>
        HTML;
    }
}
