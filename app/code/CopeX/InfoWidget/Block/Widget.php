<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Block;

use Magento\Framework\View\Element\Template;
use Magento\Widget\Block\BlockInterface;

class Widget extends Template implements BlockInterface
{
    protected $_template = 'CopeX_InfoWidget::widget.phtml';

    public function getContent(): string
    {
        return html_entity_decode((string) $this->getData('content'));
    }

    public function getColor()
    {
        return $this->getData('color') ?: '#00CC00';
    }
}
