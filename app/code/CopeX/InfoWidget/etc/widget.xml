<?xml version="1.0" encoding="UTF-8"?>
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="info_widget" class="CopeX\InfoWidget\Block\Widget">
        <label>Info Widget</label>
        <description>Info Widget</description>
        <parameters>
            <parameter name="color" xsi:type="block" required="false" visible="true" sort_order="10">
                <label translate="true">Background Color</label>
                <block class="CopeX\InfoWidget\Block\Color"/>
            </parameter>
            <parameter name="content" xsi:type="block" visible="true" required="true" sort_order="20">
                <label translate="true">Content</label>
                <block class="CopeX\InfoWidget\Block\Wysiwyg"/>
            </parameter>
        </parameters>
    </widget>
</widgets>
