<?php

declare(strict_types=1);

use CopeX\InfoWidget\Block\Widget;
use Magento\Framework\Escaper;

/**
 * @var $block Widget
 * @var $escaper Escaper
 */
?>

<?php if ($block->getContent()): ?>
    <div class="my-2">
        <marquee  class="p-2" style="background-color: <?= $block->escapeHtml($block->getColor()) ?>;">
            <?= $block->getContent() ?>
        </marquee>
    </div>
<?php endif; ?>
