<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Setup\Patch\Data;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Catalog\Model\Category;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Framework\Module\ModuleListInterface;

class AddCategoryAttribute implements DataPatchInterface
{
    private ModuleDataSetupInterface $moduleDataSetup;

    private EavSetupFactory $eavSetupFactory;
    private ModuleListInterface $moduleList;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup,
        EavSetupFactory $eavSetupFactory,
        ModuleListInterface $moduleList
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->moduleList = $moduleList;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        $moduleInfo = $this->moduleList->getOne('CopeX_InfoWidget');
        $currentVersion = $moduleInfo['setup_version'];
        if (version_compare($currentVersion, '1.0.1', '>=')) {
            /** @var \Magento\Eav\Setup\EavSetup $eavSetup */
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
            $eavSetup->addAttribute(
                Category::ENTITY,
                'empty_category_message',
                [
                    "type" => "text",
                    "label" => "Empty Category Information Message",
                    "visible" => true,
                    "visible_on_front" => true,
                    "input" => "textarea",
                    "required" => false,
                    "sort_order" => 10,
                    "global" => ScopedAttributeInterface::SCOPE_STORE,
                    "is_wysiwyg_enabled" => true,
                    "is_html_allowed_on_front" => true,
                    "group" => "General Information"
                ]
            );
        }
        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public static function getDependencies(): array
    {
        return [];
    }

    public function getAliases(): array
    {
        return [];
    }
}

