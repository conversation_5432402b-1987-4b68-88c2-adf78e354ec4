<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Plugin;

use Magento\Catalog\Api\Data\CategoryInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Framework\Api\ExtensionAttributesFactory;

class CategoryExtensionAttributePlugin
{
    protected ExtensionAttributesFactory $extensionFactory;

    public function __construct(ExtensionAttributesFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    public function afterGet(CategoryRepositoryInterface $subject, CategoryInterface $category): CategoryInterface
    {
        $extensionAttributes = $category->getExtensionAttributes();
        if (!$extensionAttributes) {
            $extensionAttributes = $this->extensionFactory->create(CategoryInterface::class);
        }
        $eavValue = $category->getData('empty_category_message');
        $extensionAttributes->setEmptyCategoryMessage($eavValue);
        $category->setExtensionAttributes($extensionAttributes);

        return $category;
    }
}
