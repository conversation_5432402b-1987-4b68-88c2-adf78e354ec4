<?php
/**
 * Blocks customer after he did register and unblock him by activating his account in backend
 * Copyright (C) 2017  CopeX
 *
 * This file is part of CopeX/RegistrationHandler.
 *
 * CopeX/RegistrationHandler is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

namespace CopeX\RegistrationHandler\Plugin\Magento\Customer\Controller\Account;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Customer;
use Magento\Framework\UrlFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Controller\Result\RedirectFactory;

class LoginPost
{


    protected $customer,
        $storeManager,
        $resultRedirectFactory,
        $messageManager,
        $repository,
        $urlModel;

    public function __construct(Customer $customer,
                                StoreManagerInterface $storeManager,
                                UrlFactory $urlFactory,
                                RedirectFactory $redirectFactory,
                                ManagerInterface $messageManager)
    {
        $this->storeManager = $storeManager;
        $this->customer = $customer;
        $this->urlModel = $urlFactory->create();
        $this->resultRedirectFactory = $redirectFactory;
        $this->messageManager = $messageManager;
    }


    public function aroundExecute(
        \Magento\Customer\Controller\Account\LoginPost $subject,
        \Closure $proceed
    )
    {
        $login = $subject->getRequest()->getPost('login');

        if (!empty($login['username']) && !empty($login['password'])) {

            $customer = $this->customer;
            if ($this->storeManager->getStore()->getWebsiteId()) {
                $customer->setWebsiteId($this->storeManager->getStore()->getWebsiteId());
            }
            $customer->loadByEmail($login['username']);
            if ($customer->getId()) {
                if ($customer->getIsApproved()) {
                    return $proceed();
                } else {
                    return $this->messageRedirect("Your account is not activated yet!");
                }
            } else {
                return $this->messageRedirect("You do not have an account yet!");
            }
        }
    }

    private function messageRedirect($message)
    {
        $this->messageManager->addErrorMessage(
            __($message)
        );
        $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

}
