<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 24.09.18
 * Time: 13:38
 */

namespace CopeX\GroupLimiter\Block;

use CopeX\GroupLimiter\Helper\Data;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Framework\View\Element\Template;
use Magento\Catalog\Model\Product\Type;

class BackInfo extends \Magento\Framework\View\Element\Template
{
    protected $productRepository;
    protected $request;
    protected $view;
    protected $dataHelper;

    public function __construct(Template\Context $context,
        \Magento\Catalog\Model\ProductRepository $productRepository,
        \Magento\Catalog\Block\Product\View $view,
        Data $dataHelper, array $data = []
    ) {
        $this->productRepository = $productRepository;
        $this->view = $view;
        $this->dataHelper = $dataHelper;
        parent::__construct($context, $data);
    }


    public function getItems() {

        $product = $this->view->getProduct();
        if ( $product && $product->getTypeId() == Type::TYPE_SIMPLE) {
            $parentItems = $this->dataHelper->getParentGrouped($product->getId());
            if ($parentItems->count()) {
                $parentProducts = [];
                foreach($parentItems as $parentItem){
                    $product = $this->productRepository->getById($parentItem->getProductId());
                    if ($product->getStatus() == Status::STATUS_ENABLED &&
                        $product->getVisibility() != Visibility::VISIBILITY_NOT_VISIBLE) {
                        $parentProducts[] = $product;
                    }
                }
                return $parentProducts;
            }
        }

        return [];
    }


}
