<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types = 1);

namespace CopeX\Winline\Console\Command;

use Magento\Customer\Model\ResourceModel\Customer\Collection;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\App\AreaList;

class Sendconfirm extends Command
{

    const CUSTOMER_EMAIL = "name";
    const EMAIL = 'email';
    /**
     * @var State
     */
    protected $state;
    /**
     * @var AreaList
     */
    protected $areaList;
    /**
     * @var Collection
     */
    private $collection;

    public function __construct(
        Collection $collection,
        State $state,
        AreaList $areaList,
        string $name = null
    ) {
        parent::__construct($name);
        $this->collection = $collection;
        $this->state = $state;
        $this->areaList = $areaList;
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $email = $input->getArgument(self::CUSTOMER_EMAIL);
        /** @var $entity \Magento\Customer\Model\Customer */
        try {
            $this->state->setAreaCode(Area::AREA_FRONTEND);
            $this->areaList->getArea(Area::AREA_FRONTEND)->load(Area::PART_TRANSLATE);
            $entity = $this->collection->addAttributeToSelect('*')->addFieldToFilter("email",$email)->getFirstItem();
            $entity->sendNewAccountEmail('confirmed');
        } catch (NoSuchEntityException $e) {
        } catch (LocalizedException $e) {
        }
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("copex_winline:sendconfirm");
        $this->setDescription("send customer confirm email");
        $this->setDefinition([
            new InputArgument(self::CUSTOMER_EMAIL, InputArgument::REQUIRED, "Customer EMAIL"),
        ]);
        parent::configure();
    }
}
