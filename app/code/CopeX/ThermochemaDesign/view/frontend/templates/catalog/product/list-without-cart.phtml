<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\App\Action\Action;

// @codingStandardsIgnoreFile
?>
<?php
/**
 * Product list template
 * @var $block \Magento\Catalog\Block\Product\ListProduct
 */
?>
<?php
$_productCollection = $block->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$infoWidgetHelper = $this->helper('CopeX\InfoWidget\Helper\Data');
?>
<?php if (!$_productCollection->count()): ?>
    <?php $emptyMessageContent = $infoWidgetHelper->contentRenderer();?>
    <?php if ($emptyMessageContent && $emptyMessageContent != ""): ?>
        <?= $emptyMessageContent ?>
    <?php else: ?>
        <?php $emptyDefaultMsg = $infoWidgetHelper->getEmptyDefaultMsg();?>
        <?php if ($emptyDefaultMsg && $emptyDefaultMsg != ""): ?>
            <div class="message info empty">
                <div><?= /* @escapeNotVerified */$emptyDefaultMsg ?></div>
            </div>
        <?php else: ?>
            <div class="message info empty">
                <div><?= /* @escapeNotVerified */
                    __('We can\'t find products matching the selection.') ?></div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php else: ?>
    <?php echo $block->getToolbarHtml() ?>
    <?php echo $block->getAdditionalHtml() ?>
    <?php
    if ($block->getMode() == 'grid') {
        $viewMode = 'grid';
        $image = 'category_page_grid';
        $showDescription = false;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
    } else {
        $viewMode = 'list';
        $image = 'category_page_list';
        $showDescription = true;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
    }
    /**
     * Position for actions regarding image size changing in vde if needed
     */
    $pos = $block->getPositioned();
    ?>
    <div class="products wrapper <?php /* @escapeNotVerified */
    echo $viewMode; ?> products-<?php /* @escapeNotVerified */
    echo $viewMode; ?>">
        <?php $iterator = 1; ?>
        <ol class="products list items product-items">
            <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
            <?php foreach ($_productCollection as $_product): ?>
                <?php /* @escapeNotVerified */
                echo ($iterator++ == 1) ? '<li class="item product product-item">'
                    : '</li><li class="item product product-item">' ?>
                <div class="product-item-info" data-container="product-grid">
                    <?php
                    $productImage = $block->getImage($_product, $image);
                    if ($pos != null) {
                        $position = ' style="left:' . $productImage->getWidth() . 'px;'
                                    . 'top:' . $productImage->getHeight() . 'px;"';
                    }
                    ?>
                    <?php // Product Image ?>
                    <a href="<?php /* @escapeNotVerified */
                    echo $_product->getProductUrl() ?>" class="product photo product-item-photo" tabindex="-1">
                        <?php echo $productImage->toHtml(); ?>
                    </a>
                    <div class="product details product-item-details">
                        <?php echo $_product->getSku(); ?><br/>
                        <?php
                        $_productNameStripped = $block->stripTags($_product->getName(), null, true);
                        ?>
                        <strong class="product name product-item-name">
                            <a class="product-item-link"
                               href="<?php /* @escapeNotVerified */
                               echo $_product->getProductUrl() ?>">
                                <?php /* @escapeNotVerified */
                                echo $_helper->productAttribute($_product, $_product->getName(), 'name'); ?>
                            </a>
                        </strong>
                        <?php echo $block->getReviewsSummaryHtml($_product, $templateType); ?>
                        <?php /* @escapeNotVerified */
                        echo $block->getProductPrice($_product) ?>
                        <?php echo $block->getProductDetailsHtml($_product); ?>

                        <div class="product-item-inner">
                            <div class="product actions product-item-actions"<?php echo strpos($pos,
                                $viewMode . '-actions') ? $position : ''; ?>>
                                <div class="actions-primary"<?php echo strpos($pos, $viewMode . '-primary') ? $position
                                    : ''; ?>>
                                    <?php if ($_product->isSaleable()): ?>
                                        <?php $postParams = $block->getAddToCartPostParams($_product); ?>
                                    <?php endif; ?>
                                </div>
                                <div data-role="add-to-links"
                                     class="actions-secondary"<?php echo strpos($pos, $viewMode . '-secondary')
                                    ? $position : ''; ?>>
                                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                        <?php echo $addToBlock->setProduct($_product)->getChildHtml(); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if ($showDescription): ?>
                                <div class="product description product-item-description">
                                    <?php /* @escapeNotVerified */
                                    echo $_helper->productAttribute($_product, $_product->getShortDescription(),
                                        'short_description') ?>
                                    <a href="<?php /* @escapeNotVerified */
                                    echo $_product->getProductUrl() ?>" title="<?php /* @escapeNotVerified */
                                    echo $_productNameStripped ?>"
                                       class="action more"><?php /* @escapeNotVerified */
                                        echo __('Learn More') ?></a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php echo ($iterator == count($_productCollection) + 1) ? '</li>' : '' ?>
            <?php endforeach; ?>
        </ol>
    </div>
    <?php echo $block->getToolbarHtml() ?>
    <?php if (!$block->isRedirectToCartEnabled()) : ?>
        <script type="text/x-magento-init">
        {
            "[data-role=tocart-form], .form.map.checkout": {
                "catalogAddToCart": {}
            }
        }



        </script>
    <?php endif; ?>
<?php endif; ?>
