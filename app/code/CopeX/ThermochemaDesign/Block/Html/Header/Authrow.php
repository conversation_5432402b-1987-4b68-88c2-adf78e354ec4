<?php
/**
 * Created by PhpStorm.
 * User: stephan-copex
 * Date: 06.07.17
 * Time: 07:34
 */

namespace CopeX\ThermochemaDesign\Block\Html\Header;


use CopeX\Successionproduct\Helper\SuccessionHelper;
use Magento\Framework\View\Element\Template;

class Authrow extends \Magento\Framework\View\Element\Template
{
    /**
     * Current template name
     *
     * @var string
     */
    protected $_template = 'html/header/authrow.phtml';
    protected $successionHelper;

    protected function _toHtml()
    {
        return parent::_toHtml(); // TODO: Change the autogenerated stub
    }

    public function __construct(Template\Context $context, SuccessionHelper $successionHelper, array $data = [] )
    {
        $this->successionHelper = $successionHelper;
        parent::__construct($context, $data);
    }

    public function getSuccessionHelper()
    {
        return $this->successionHelper;
    }


}