<?php
/**
 * Created by PhpStorm.
 * User: stephan-copex
 * Date: 18.07.17
 * Time: 10:04
 */

namespace CopeX\ThermochemaDesign\Block\Catalog\Product;

use CopeX\Successionproduct\Helper\SuccessionHelper;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Registry;

class ProductAlert extends \Magento\Framework\View\Element\Template
{
    protected $successionHelper, $registry;


    public function __construct(Template\Context $context, SuccessionHelper $successionHelper, Registry $registry, array $data = [])
    {
        $this->successionHelper = $successionHelper;
        $this->registry = $registry;
        parent::__construct($context, $data);
    }


    public function getSuccessionHelper()
    {
        return $this->successionHelper;
    }

    public function getCurrentProduct()
    {
        return $this->registry->registry("product");
    }


}