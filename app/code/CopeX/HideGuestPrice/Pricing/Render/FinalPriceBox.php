<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace CopeX\HideGuestPrice\Pricing\Render;

use Magento\Catalog\Model\Product\Pricing\Renderer\SalableResolverInterface;
use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Pricing\SaleableInterface;
use Magento\Framework\Pricing\Price\PriceInterface;
use Magento\Framework\Pricing\Render\RendererPool;
use Magento\Catalog\Pricing\Price\MinimalPriceCalculatorInterface;


/**
 * Class for final_price rendering
 *
 * @method bool getUseLinkForAsLowAs()
 * @method bool getDisplayMinimalPrice()
 */
class FinalPriceBox extends \Magento\Catalog\Pricing\Render\FinalPriceBox
{

    protected $_session;

    /**
     * FinalPriceBox constructor.
     * @param Context                               $context
     * @param SaleableInterface                     $saleableItem
     * @param PriceInterface                        $price
     * @param RendererPool                          $rendererPool
     * @param array                                 $data
     * @param SalableResolverInterface|null         $salableResolver
     * @param MinimalPriceCalculatorInterface|null  $minimalPriceCalculator
     * @param \Magento\Customer\Model\Session\Proxy $session
     */
    public function __construct(
        Context $context,
        SaleableInterface $saleableItem,
        PriceInterface $price,
        RendererPool $rendererPool,
        SalableResolverInterface $salableResolver = null,
        MinimalPriceCalculatorInterface $minimalPriceCalculator = null,
        \Magento\Customer\Model\Session\Proxy $session,
        array $data = []
    )
    {
        parent::__construct($context, $saleableItem, $price, $rendererPool, $data, $salableResolver, $minimalPriceCalculator);
        $this->_session = $session;
    }

    /**
     * Wrap with standard required container
     *
     * @param string $html
     * @return string
     */
    public function wrapResult($html)
    {
        if ($this->_session->isLoggedIn()) {
            return '<div class="price-box ' . $this->getData('css_classes') . '" ' .
                'data-role="priceBox" ' .
                'data-product-id="' . $this->getSaleableItem()->getId() . '"' .
                '>' . $html . '</div>';
        } else {
            return __("Please login to view price.");
        }
    }
}
