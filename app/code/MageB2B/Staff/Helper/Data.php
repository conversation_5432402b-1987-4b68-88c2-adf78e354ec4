<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Helper;
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /*
     * set current staff model
     *
     * @var \MageB2B\Staff\Model\Staff
     */
    protected $_currentStaff;

    /** @var \MageB2B\Staff\Helper\Common */
    protected $_common;

    /** @var \MageB2B\Staff\Model\StaffRepository */
    protected $staffRepository;


    public function __construct(
        \MageB2B\Staff\Helper\Common $common,
        \MageB2B\Staff\Model\StaffRepository $staffRepository
    )
    {
        $this->_common = $common;
        $this->staffRepository = $staffRepository;
    }

    /**
     * checks if the current customer
     * has a sales staff assigned
     * @return bool
     */
    public function hasSalesStaff()
    {
        if ($this->getCustomerSession()->getStaffId()) {
            return true;
        }

        try {
            $staff = $this->staffRepository->getByField('email', $this->getCustomerSession()->getCustomer()->getEmail());
            return $staff->getId() ? true : false;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * checks if the current customer
     * has a sales staff assigned
     * @return bool
     */
    public function getSalesStaff()
    {
        if ($this->getCustomerSession()->getStaffId()) {
            try {
                $staff = $this->staffRepository->getByField('staff_id', $this->getCustomerSession()->getStaffId());
                return $staff;
            } catch (\Exception $e) {
                return false;
            }
        }
    }


    /**
     * @return \Magento\Customer\Model\Session
     */
    public function getCustomerSession()
    {
        return $this->_common->getCustomerSession();
    }

    /**
     * @param string $node
     * @param string $fieldset
     * @param string $base
     * @return mixed
     */
    public function getStoreConfig($node = '', $fieldset = 'general', $base = 'staff')
    {
        $config = $this->_common->getConfigHelper();
        if ($node == '') {
            return $config->get($base . "/" . $fieldset);
        }
        return $config->get($base . "/" . $fieldset . "/" . $node);
    }

    /*
     * commission type config setting
     * return string | total_excl_tax, total_incl_tax or product_dicount
     */
    public function getCommissionType()
    {
        return $this->getStoreConfig('commission_type');
    }

    /*
     * email order to config setting
     * return int
     */
    public function getEmailOrderTo()
    {
        return $this->getStoreConfig('email_order_to', 'email');
    }

    /**
     * return boolean
     */
    public function isSearchCustomersByStaffEnable()
    {
        return $this->getStoreConfig('search_customers_by_staff');
    }

    /**
     * return config customer have multiple staff
     *
     * return boolean
     */
    public function canCustomerHaveMultipleStaff()
    {
        return $this->getStoreConfig('customer_have_multiplestaff');
    }

    /**
     * Calculate and return staff commission amount
     *
     * @param $total
     * @return float|int
     */
    public function getStaffCommission($total)
    {
        $staff = $this->getCurrentStaff();
        if ($staff) {
            $commission = $staff->getCommission();
            $commissionAmount = ($commission * $total) / 100;
            return $commissionAmount;
        }
        return 0;
    }

    /**
     * get current staff
     */
    public function getCurrentStaff()
    {
        if (!$this->_currentStaff) {
            $staffId = $this->getCustomerSession()->getStaffId();
            if ($staffId) {
                $this->_currentStaff = $this->staffRepository->getById($staffId);
            } else {
                $this->_currentStaff = null;
            }
        }
        return $this->_currentStaff;
    }

    /**
     * function to return the pagination string
     *
     * @param int $page
     * @param $totalitems
     * @param int $limit
     * @param int $adjacents
     * @param string $targetpage
     * @param string $pagestring
     * @return string
     */
    public function getPaginationString($page = 1, $totalitems = 0, $limit = 15, $adjacents = 1, $targetpage = "/", $pagestring = "?page=")
    {
        //defaults
        if (!$adjacents) {
            $adjacents = 1;
        }
        if (!$limit) {
            $limit = 15;
        }
        if (!$page) {
            $page = 1;
        }
        if (!$targetpage) {
            $targetpage = "/";
        }

        //other vars
        $prev = $page - 1;                                    //previous page is page - 1
        $next = $page + 1;                                    //next page is page + 1
        $lastpage = ceil($totalitems / $limit);                //lastpage is = total items / items per page, rounded up.
        $lpm1 = $lastpage - 1;                                //last page minus 1

        /**
         * Now we apply our rules and draw the pagination object.
         * We're actually saving the code to a variable in case we want to draw it more than once.
         */
        $pagination = "";
        if ($lastpage > 1) {
            $pagination .= "<div class=\"pagination\"";
            $pagination .= ">";

            //previous button
            if ($page > 1) {
                $pagination .= "<a href=\"$targetpage$pagestring$prev\">" . __('prev') . "</a>";
            } else {
                $pagination .= "<span class=\"disabled\">" . __('prev') . "</span>";
            }

            //pages
            if ($lastpage < 7 + ($adjacents * 2))    //not enough pages to bother breaking it up
            {
                for ($counter = 1; $counter <= $lastpage; $counter++) {
                    if ($counter == $page) {
                        $pagination .= "<span class=\"current\">$counter</span>";
                    } else {
                        $pagination .= "<a href=\"" . $targetpage . $pagestring . $counter . "\">$counter</a>";
                    }
                }
            } elseif ($lastpage >= 7 + ($adjacents * 2))    //enough pages to hide some
            {
                //close to beginning; only hide later pages
                if ($page < 1 + ($adjacents * 3)) {
                    for ($counter = 1; $counter < 4 + ($adjacents * 2); $counter++) {
                        if ($counter == $page)
                            $pagination .= "<span class=\"current\">$counter</span>";
                        else
                            $pagination .= "<a href=\"" . $targetpage . $pagestring . $counter . "\">$counter</a>";
                    }
                    $pagination .= "<span class=\"elipses\">...</span>";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . $lpm1 . "\">$lpm1</a>";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . $lastpage . "\">$lastpage</a>";
                } //in middle; hide some front and some back
                elseif ($lastpage - ($adjacents * 2) > $page && $page > ($adjacents * 2)) {
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . "1\">1</a>";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . "2\">2</a>";
                    $pagination .= "<span class=\"elipses\">...</span>";
                    for ($counter = $page - $adjacents; $counter <= $page + $adjacents; $counter++) {
                        if ($counter == $page) {
                            $pagination .= "<span class=\"current\">$counter</span>";
                        } else {
                            $pagination .= "<a href=\"" . $targetpage . $pagestring . $counter . "\">$counter</a>";
                        }
                    }
                    $pagination .= "...";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . $lpm1 . "\">$lpm1</a>";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . $lastpage . "\">$lastpage</a>";
                } //close to end; only hide early pages
                else {
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . "1\">1</a>";
                    $pagination .= "<a href=\"" . $targetpage . $pagestring . "2\">2</a>";
                    $pagination .= "<span class=\"elipses\">...</span>";
                    for ($counter = $lastpage - (1 + ($adjacents * 3)); $counter <= $lastpage; $counter++) {
                        if ($counter == $page) {
                            $pagination .= "<span class=\"current\">$counter</span>";
                        } else {
                            $pagination .= "<a href=\"" . $targetpage . $pagestring . $counter . "\">$counter</a>";
                        }
                    }
                }
            }
            //next button
            if ($page < $counter - 1) {
                $pagination .= "<a href=\"" . $targetpage . $pagestring . $next . "\">" . __('next') . " </a>";
            } else {
                $pagination .= "<span class=\"disabled\">" . __('next') . " ?</span>";
            }
            $pagination .= "</div>\n";
        }

        return $pagination;

    }

    /**
     * @return array|string
     */
    public function getEntityTypeForStaff()
    {
        // preprint('Add staff as 1 entity in eav_entity_type table to get entity_id of it. To which staff will be saved in customer table. Before we were using entity_type_id = 0 but now we want some id from eav_entity_type table.');
        // die();
        return $this->getStoreConfig('entity_type_id');
    }
}
