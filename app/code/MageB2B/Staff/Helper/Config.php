<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Staff\Helper;

class Config extends \Magento\Framework\App\Helper\AbstractHelper
{
	/**
	 * @var \Magento\Framework\App\Config\ScopeConfigInterface
	 */
	protected $_scopeConfig;
	
	/**
	 * @var \Magento\Config\Model\ResourceModel\Config 
	 */
	protected $_resourceConfig;
	
	/**
	 * @var \Magento\Store\Model\StoreManagerInterface
	 */
    protected $_storeManager;

	/**
     * Initialize dependencies.
     */
    public function __construct(
		\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
		\Magento\Config\Model\ResourceModel\Config $resourceConfig,
		\Magento\Store\Model\StoreManagerInterface $storeManager
	) {
		$this->_scopeConfig = $scopeConfig;
		$this->_resourceConfig = $resourceConfig;
		$this->_storeManager = $storeManager;
    }
	
	public function get($path, $scope = null, $storeCode = null) {
		if (!$scope) {
			$scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
		}
		
		if (!$storeCode) {
			$storeCode = $this->getCurrentStore()->getCode();
		}
		
		return $this->_scopeConfig->getValue($path, $scope, $storeCode);
	}
	
	public function save($path, $value, $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeId = null) {
		return $this->_resourceConfig->saveConfig(
			$path,
			$value,
			$scope,
			$storeId
		);
	}
	
	public function getCurrentStore()
	{
		return $this->_storeManager->getStore();
	}
	
	public function getStore($storeId)
	{
		if ($storeId && $storeId > 0) {
			return $this->_storeManager->getStore($storeId);
		}
		return $this->getCurrentStore();
	}
}
