<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

use Magento\Framework\App\Action\Action;
// @codingStandardsIgnoreFile

?>
<?php
/**
 * Product list template
 * @var $block \Magento\Catalog\Block\Product\ListProduct
 */


$currencyFormatter = $this->helper('Magento\Framework\Pricing\Helper\Data');
?>
<?php
$_productCollection = $block->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$infoWidgetHelper = $this->helper('CopeX\InfoWidget\Helper\Data');
?>
<?php if (!$_productCollection->count()): ?>
    <?php $emptyMessageContent = $infoWidgetHelper->contentRenderer();?>
    <?php if ($emptyMessageContent && $emptyMessageContent != ""): ?>
        <?= $emptyMessageContent ?>
    <?php else: ?>
        <?php $emptyDefaultMsg = $infoWidgetHelper->getEmptyDefaultMsg();?>
        <?php if ($emptyDefaultMsg && $emptyDefaultMsg != ""): ?>
            <div class="message info empty">
                <div><?= /* @escapeNotVerified */$emptyDefaultMsg ?></div>
            </div>
        <?php else: ?>
            <div class="message info empty">
                <div><?= /* @escapeNotVerified */
                    __('We can\'t find products matching the selection.') ?></div>
            </div>
        <?php endif; ?>
    <?php endif; ?>
<?php else: ?>
    <?= $block->getToolbarHtml() ?>
    <?= $block->getAdditionalHtml() ?>
    <?php
    if ($block->getMode() == 'grid') {
        $viewMode = 'grid';
        $image = 'category_page_grid';
        $showDescription = false;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
    } else {
        $viewMode = 'list';
        $image = 'category_page_list';
        $showDescription = true;
        $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::FULL_VIEW;
    }
    /**
     * Position for actions regarding image size changing in vde if needed
     */
    $pos = $block->getPositioned() ?? "";
    ?>
    <div class="products wrapper <?= /* @escapeNotVerified */
    $viewMode ?> products-<?= /* @escapeNotVerified */
    $viewMode ?>">
        <?php $iterator = 1; ?>
        <ol class="products list items product-items">
            <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
            <?php foreach ($_productCollection as $_product): ?>
                <?= /* @escapeNotVerified */
                ($iterator++ == 1) ? '<li class="item product product-item">'
                    : '</li><li class="item product product-item">' ?>
                <div class="product-item-info" data-container="product-grid">
                    <?php
                    $productImage = $block->getImage($_product, $image);
                    if ($pos != null) {
                        $position = ' style="left:' . $productImage->getWidth() . 'px;'
                            . 'top:' . $productImage->getHeight() . 'px;"';
                    }
                    ?>
                    <?php // Product Image ?>
                    <a href="<?= /* @escapeNotVerified */
                    $_product->getProductUrl() ?>" class="product photo product-item-photo" tabindex="-1">
                        <?= $productImage->toHtml() ?>
                    </a>
                    <div class="product details product-item-details">
                        <a class="product-item-link link-sku"
                           href="<?= /* @escapeNotVerified */
                           $_product->getProductUrl() ?>"><?php echo $_helper->productAttribute($_product,
                                $_product->getSku(), 'sku') ?></a><br/>
                        <?php
                        $_productNameStripped = $block->stripTags($_product->getName(), null, true);
                        ?>
                        <strong class="product name product-item-name">
                            <a class="product-item-link"
                               href="<?= /* @escapeNotVerified */
                               $_product->getProductUrl() ?>">
                                <?= /* @escapeNotVerified */
                                $_helper->productAttribute($_product, $_product->getName(), 'name') ?>
                            </a>
                        </strong>
                        <?= $block->getReviewsSummaryHtml($_product, $templateType) ?>
                        <?php echo $block->getProductPrice($_product); ?>
                        <?= $block->getProductDetailsHtml($_product) ?>

                        <div class="product-item-inner">
                            <div class="product actions product-item-actions"<?= strpos($pos, $viewMode . '-actions')
                                ? $position : '' ?>>
                                <div class="actions-primary"<?= strpos($pos, $viewMode . '-primary') ? $position
                                    : '' ?>>
                                    <?php if ($_product->isSaleable()): ?>
                                        <?php $postParams = $block->getAddToCartPostParams($_product); ?>
                                        <form data-role="tocart-form"
                                              data-product-sku="<?= $block->escapeHtml($_product->getSku()) ?>"
                                              action="<?= /* @NoEscape */
                                              $postParams['action'] ?>" method="post">
                                            <input type="hidden" name="product" value="<?= /* @escapeNotVerified */
                                            $postParams['data']['product'] ?>">
                                            <input type="hidden" name="<?= /* @escapeNotVerified */
                                            Action::PARAM_NAME_URL_ENCODED ?>" value="<?= /* @escapeNotVerified */
                                            $postParams['data'][Action::PARAM_NAME_URL_ENCODED] ?>">
                                            <?= $block->getBlockHtml('formkey') ?>
                                            <button type="submit"
                                                    title="<?= $block->escapeHtml(__('Add to Cart')) ?>"
                                                    class="action tocart primary">
                                                <span><?= /* @escapeNotVerified */
                                                    __('Add to Cart') ?></span>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                </div>
                                <div data-role="add-to-links" class="actions-secondary"<?= strpos($pos,
                                    $viewMode . '-secondary') ? $position : '' ?>>
                                    <?php if ($addToBlock = $block->getChildBlock('addto')): ?>
                                        <?= $addToBlock->setProduct($_product)->getChildHtml() ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <?php if ($showDescription): ?>
                                <div class="product description product-item-description">
                                    <?= /* @escapeNotVerified */
                                    $_helper->productAttribute($_product, $_product->getShortDescription(),
                                        'short_description') ?>
                                    <a href="<?= /* @escapeNotVerified */
                                    $_product->getProductUrl() ?>" title="<?= /* @escapeNotVerified */
                                    $_productNameStripped ?>"
                                       class="action more"><?= /* @escapeNotVerified */
                                        __('Learn More') ?></a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?= ($iterator == count($_productCollection) + 1) ? '</li>' : '' ?>
            <?php endforeach; ?>
        </ol>
    </div>
    <?= $block->getChildBlock('toolbar')->setIsBottom(true)->toHtml() ?>
    <?php if (!$block->isRedirectToCartEnabled()) : ?>
        <script type="text/x-magento-init">
        {
            "[data-role=tocart-form], .form.map.checkout": {
                "catalogAddToCart": {
                    "product_sku": "<?= /* @NoEscape */
            $_product->getSku() ?>"
                }
            }
        }





        </script>
    <?php endif; ?>
<?php endif; ?>
