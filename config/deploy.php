<?php

namespace Deployer;

use Deployer\Task\Context;

require 'recipe/magento2.php';
//require __DIR__ . '/vendor/deployer/deployer/contrib/rsync.php';
const ENV_PRODUCTION = 'production';
const ENV_STAGING = 'staging';
/**
 * Settings
 */
set('docker',false);
set('application', 'thermochema');
set('repository', '*******************:copex/thermochema.git');
set('deployment_root_path', '/home/<USER>');
set('keep_releases', 2);
set('php_version', "7.4");
set('writable_mode','skip');

set('bin/php', function(){
    return "/home/<USER>/.bin/php";
});

//deploy targets
include __DIR__ . '/deploy/production.php';
include __DIR__ . '/deploy/staging.php';
//custom tasks
include __DIR__ . '/deploy/tasks/dbTasks.php';
include __DIR__ . '/deploy/tasks/miscTasks.php';

//composer command
set('bin/composer', function () {
    return '/home/<USER>/.bin/composer';
});

set('shared_files',[
    '{{magento_dir}}/app/etc/env.php',
    '{{magento_dir}}/var/.maintenance.ip',
    '{{magento_dir}}/.htpasswd'
]);

// which languages should get deployed
set('static_content_locales', 'de_DE');

// themes to deploy
set('magento_themes', ['Magento/backend', "CopeX/ThermochemaDesign"]);

//copy env.php file
task('copy_config', function () {
    $env = get('labels')['env'];
    if ($env == ENV_PRODUCTION) {
        upload('config/etc/env.prod.php', '{{release_or_current_path}}/app/etc/env.php');
    }
    if ($env == ENV_STAGING) {
        upload('config/etc/env.staging.php', '{{release_or_current_path}}/app/etc/env.php');
    }
});

task('restart_webserver', function () {
    $docker = get('docker');
    if ($docker) {
        run("cd {{deploy_path}}/docker && docker-compose restart app");
    }
    else {
        $env = get('labels')['env'];
        if ($env == ENV_PRODUCTION) {
            run("sudo /home/<USER>/scripts/restart-fpm.sh");
        }
        if ($env == ENV_STAGING) {
            run("sudo /home/<USER>/scripts/restart-fpm.sh");
        }
    }
});

task('disable_fpc', function () {
    run("{{release_or_current_path}}/bin/magento cache:disable full_page");
});

after('deploy:failed', 'deploy:unlock');
before('magento:compile', 'copy_config');
after('deploy:symlink', 'restart_webserver');
after('deploy:failed', 'deploy:unlock');
after('magento:maintenance:disable','disable_fpc');
after('magento:maintenance:disable','restart_webserver');
